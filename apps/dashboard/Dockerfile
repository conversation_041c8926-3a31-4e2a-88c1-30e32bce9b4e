#### BASE STAGE
#### Installs moon.

FROM node:24 AS base
WORKDIR /app

# Install moon binary
RUN npm install -g @moonrepo/cli

#### SKELETON STAGE
#### Scaffolds repository skeleton structures.

FROM base AS skeleton

# Copy entire repository and scaffold
COPY . .
RUN moon docker scaffold dashboard

#### BUILD STAGE
#### Builds the project.

FROM base AS build
# Copy toolchain
COPY --from=skeleton /root/.proto /root/.proto

# Copy workspace configs
COPY --from=skeleton /app/.moon/docker/workspace .

# Install dependencies
RUN moon docker setup

# Copy project sources
COPY --from=skeleton /app/.moon/docker/sources .
COPY tsconfig.json .
COPY tsconfig.base.json .

# Build the project
RUN moon run dashboard:build

# Prune extraneous dependencies
RUN moon docker prune

#### START STAGE
#### Runs the project.

FROM nginx:stable-alpine

RUN sed -i '1idaemon off;' /etc/nginx/nginx.conf
COPY --from=skeleton /app/kubernetes/nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=build /app/apps/dashboard/dist/ /app

EXPOSE 80

CMD ["nginx"]
