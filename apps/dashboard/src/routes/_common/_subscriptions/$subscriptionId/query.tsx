/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { ObservableHint } from '@legendapp/state'
import { use$ } from '@legendapp/state/react'
import { subscriptionsApi } from '@mass/api'
import { ALLOWED_DAY_COUNT, SubscriptionResults, subscription$ } from '@mass/components/dashboard'
import { Button, Checkbox, DatePicker, Radio, RadioGroup, Select, Text, Title } from '@mass/components/shared'
import { useDate, utcDate } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

function SubscriptionQuery() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const format = useDate('MMMM YYYY')
  const limits = use$(() => subscription$.usageLimits.max.get())
  const lastXPeriods = use$(() => ({
    monthly: subscription$.usageLimits.prev.month.get(),
    daily: subscription$.usageLimits.prev.day.get(),
  }))
  const { period, isRange, isLastX, selectedDate } = use$(() => subscription$.query.values.get())
  const {
    period: periodError,
    isRange: isRangeError,
    isLastX: isLastXError,
    selectedDate: selectedDateError,
  } = use$(() => subscription$.query.errors.get())

  const isLoading = use$(() => subscription$.usage.isLoading.get())

  const startYearOptions = use$(() => subscription$.query.startYearOptions.get())
  const startMonthOptions = use$(() => subscription$.query.startMonthOptions.get())
  const endMonthOptions = use$(() => subscription$.query.endMonthOptions.get())

  const handleClear = () => {
    subscription$.query.clear()
    subscription$.usage.clear()
  }

  const handleApply = async () => {
    try {
      await toast.promise(
        async () => {
          const timeStart = performance.now()

          subscription$.usage.isLoading.set(true)

          subscription$.query.setDirty('period')
          subscription$.query.setDirty('isRange')
          subscription$.query.setDirty('isLastX')
          subscription$.query.setDirty('selectedDate')

          if (Object.entries(subscription$.query.errors.get()).some(([, value]) => value)) {
            return toast.error(common('fill-all-required-fields'))
          }

          const populated = subscription$.query.populated.get()

          if (!populated) {
            throw new Error('something-went-wrong')
          }

          const usageData = await subscriptionsApi.usage.data({
            query: {
              // biome-ignore lint/style/noNonNullAssertion: Redundant
              id: subscription$.selectedSubscription.get()!.id,
            },

            params: populated,
          })

          subscription$.usage.data.set(ObservableHint.plain(usageData))

          const timeEnd = performance.now()

          if (timeEnd - timeStart < 200) {
            await new Promise(resolve => setTimeout(resolve, 300))
          }
        },
        {
          loading: common('wait'),
          success: common('operation-completed'),
          error: common('something-went-wrong'),
        },
      )
    } catch (err) {
      console.log(err)
    } finally {
      subscription$.usage.isLoading.set(false)
    }
  }

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-16 md:p-16', // spacing
      )}>
      {/* Query Params */}

      <div
        className={clsx(
          'flex flex-col justify-between lg:flex-row', // flex
          'gap-8 pb-8 md:pb-16', // spacing
          'border-accessory-1 border-b', // border
          'relative w-full overflow-hidden',
        )}>
        <div className='flex flex-col gap-2'>
          <Title el='h1' variant='h4'>
            {dashboard('subscriptions.query-params')}
          </Title>

          <Text variant='dim-2'> {dashboard('subscriptions.query-params-description')} </Text>
        </div>

        <div className='flex flex-row items-center gap-4'>
          <Button variant='bordered' className='rounded-c1' onClick={handleClear} disabled={isLoading}>
            {common('clear')}
          </Button>

          <Button variant='primary' className='rounded-c1' onClick={handleApply} disabled={isLoading}>
            {common('apply')}
          </Button>
        </div>
      </div>

      <div
        className={clsx(
          'grid grid-cols-1 md:grid-cols-2', // grid
          'gap-8 pb-8 md:gap-16 md:pb-20', // spacing
          'border-accessory-1 border-b', // border
        )}>
        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-12 md:gap-18', // spacing
          )}>
          <div className='flex flex-col gap-4 md:gap-6'>
            <Title el='h3' variant='h5'>
              {common('period-type')}
            </Title>

            <RadioGroup
              name='test-options'
              value={period}
              onChange={value => subscription$.query.setPeriod(value as Dashboard.Periods)}
              error={periodError}
              disabled={isLoading}>
              <Radio value='daily' label={common('daily')} />
              <Radio value='monthly' label={common('monthly')} />
              <Radio value='yearly' label={common('yearly')} />
            </RadioGroup>
          </div>

          <div className='flex flex-col gap-4 md:gap-6'>
            <Title el='h3' variant='h5'>
              {common('date')}
            </Title>

            {period === 'yearly' && (
              <Select
                value={selectedDate[0]?.toISOString() ?? ''}
                options={startYearOptions.map(date => {
                  return {
                    value: date.toISOString(),
                    label: date.getFullYear().toString(),
                  }
                })}
                onValueChange={value => {
                  const date = new Date(value)

                  subscription$.query.setSelectedDate([date])
                }}
                className='max-w-[350px]'
                disabled={isLoading}
              />
            )}

            {period === 'monthly' && !isRange && (
              <Select
                value={selectedDate[0]?.toISOString() ?? ''}
                options={startMonthOptions.map(date => {
                  return {
                    value: date.toISOString(),
                    label: format(date),
                  }
                })}
                onValueChange={value => {
                  const date = new Date(value)

                  subscription$.query.setSelectedDate([date])
                }}
                className='max-w-[350px]'
                disabled={isLoading}
              />
            )}

            {period === 'monthly' && isRange && (
              <div className='flex gap-4'>
                <Select
                  value={selectedDate[0]?.toISOString() ?? ''}
                  options={startMonthOptions.map(date => {
                    return {
                      value: date.toISOString(),
                      label: format(date),
                    }
                  })}
                  onValueChange={value => {
                    const date = new Date(value)

                    subscription$.query.setSelectedDate([date, selectedDate[1] || null])
                  }}
                  className='max-w-[350px]'
                  disabled={isLoading}
                />

                <Select
                  value={selectedDate[1]?.toISOString() ?? ''}
                  options={endMonthOptions.map(date => {
                    return {
                      value: date.toISOString(),
                      label: format(date),
                    }
                  })}
                  onValueChange={value => {
                    const date = new Date(value)

                    subscription$.query.setSelectedDate([selectedDate[0] || null, date])
                  }}
                  className='max-w-[350px]'
                  disabled={isLoading}
                />
              </div>
            )}

            {period === 'daily' && (
              <DatePicker
                placeholder={common(isRange ? 'start-end-date' : 'date')}
                value={selectedDate}
                onChange={value => subscription$.query.setSelectedDate(value)}
                {...(isRange ? { max: ALLOWED_DAY_COUNT, min: 2 } : {})}
                minDate={limits?.day ? utcDate().subtract(limits.day, 'day').toDate() : undefined}
                maxDate={new Date()}
                mode={isRange ? 'range' : 'single'}
                error={selectedDateError}
                className='max-w-[350px]'
                disabled={isLoading}
              />
            )}

            {period !== 'yearly' && (
              <Checkbox
                label={common('date-range')}
                checked={isRange}
                onChange={e => subscription$.query.setIsRange(e.target.checked)}
                disabled={isLastX || isLoading}
                error={isRangeError}
              />
            )}
          </div>
        </div>

        <div
          className={clsx(
            'flex flex-col', // flex
            'gap-12 md:gap-18', // spacing
          )}>
          {period !== 'yearly' && lastXPeriods?.[period] && (
            <div className='flex flex-col gap-4 md:gap-6'>
              <Title el='h3' variant='h5'>
                {common('analyze-type')}
              </Title>

              <Checkbox
                label={dashboard('subscriptions.last-x', {
                  period: common(period),
                  count: lastXPeriods[period],
                })}
                checked={isLastX}
                onChange={e => subscription$.query.setIsLastX(e.target.checked)}
                disabled={isRange || isLoading}
                error={isLastXError}
              />
            </div>
          )}
        </div>
      </div>

      {/* Results */}

      <SubscriptionResults />
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/query')({
  component: SubscriptionQuery,
})
