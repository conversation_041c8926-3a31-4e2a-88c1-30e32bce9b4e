stages:
  - lint
  - test
  - build
  - container-build
  - deploy
  - notify

variables:
  # Cache settings for better performance
  MOON_CACHE_DIR: ".moon/cache"
  BUN_INSTALL_CACHE_DIR: ".bun/install/cache"

lint:
  stage: lint
  image: node:22
  cache:
    key: "moonrepo-cache-v1"
    paths:
      - .moon/cache
      - .bun/install/cache
      - node_modules
      - .proto
    policy: pull-push
  before_script:
    # Install proto (moonrepo's toolchain manager)
    - curl -fsSL https://moonrepo.dev/install/proto.sh | bash
    - export PATH="$HOME/.proto/bin:$PATH"
    - proto --version
    # Install and setup tools via proto
    - proto use
    - export PATH="$HOME/.proto/bin:$PATH"
    # Verify installations
    - bun --version
    - moon --version
    # Sync moonrepo projects
    - moon sync projects
  script:
    - moon run :check --affected
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

test:
  stage: test
  image: node:22
  cache:
    key: "moonrepo-cache-v1"
    paths:
      - .moon/cache
      - .bun/install/cache
      - node_modules
      - .proto
    policy: pull-push
  before_script:
    # Install proto (moonrepo's toolchain manager)
    - curl -fsSL https://moonrepo.dev/install/proto.sh | bash
    - export PATH="$HOME/.proto/bin:$PATH"
    - proto --version
    # Install and setup tools via proto
    - proto use
    - export PATH="$HOME/.proto/bin:$PATH"
    # Verify installations
    - bun --version
    - moon --version
    # Sync moonrepo projects
    - moon sync projects
  script:
    - moon run :test --affected
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

build:
  stage: build
  image: node:22
  cache:
    key: "moonrepo-cache-v1"
    paths:
      - .moon/cache
      - .bun/install/cache
      - node_modules
      - .proto
    policy: pull-push
  before_script:
    # Install proto (moonrepo's toolchain manager)
    - curl -fsSL https://moonrepo.dev/install/proto.sh | bash
    - export PATH="$HOME/.proto/bin:$PATH"
    - proto --version
    # Install and setup tools via proto
    - proto use
    - export PATH="$HOME/.proto/bin:$PATH"
    # Verify installations
    - bun --version
    - moon --version
    # Sync moonrepo projects
    - moon sync projects
  script:
    # Run moonrepo builds for dashboard and admin
    - moon run dashboard:build
    # Note: Assuming you have an admin project configured in moonrepo
    # If not, you might need to add it to your workspace or adjust this line
    - moon run admin:build || echo "Admin project not found in moonrepo, skipping..."
  artifacts:
    paths:
      - apps/dashboard/dist
      - apps/admin/dist
    expire_in: 1 hour

docker-build:
  stage: container-build
  image: docker:latest
  services:
    - name: docker:dind
      alias: docker
  variables:
    DOCKER_TLS_CERTDIR: ""
  dependencies:
    - build
  before_script:
    - docker info
  script:
    - echo "Pushing to $CI_REGISTRY"
    - echo "$CI_JOB_TOKEN" | docker login -u gitlab-ci-token --password-stdin "$CI_REGISTRY"
    # Build dashboard using moonrepo-optimized Dockerfile
    - docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard" . --file apps/dashboard/Dockerfile
    - docker tag "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard" "$CI_REGISTRY_IMAGE:latest-dashboard"
    - docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard"
    - docker push "$CI_REGISTRY_IMAGE:latest-dashboard"
    # Build admin if Dockerfile exists
    - |
      if [ -f "apps/admin/Dockerfile" ]; then
        docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin" . --file apps/admin/Dockerfile
        docker tag "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin" "$CI_REGISTRY_IMAGE:latest-admin"
        docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin"
        docker push "$CI_REGISTRY_IMAGE:latest-admin"
      else
        echo "Admin Dockerfile not found, skipping admin build"
      fi

deploy:
  stage: deploy
  image:
    name: bitnami/kubectl:latest
    entrypoint: [""]
  dependencies:
    - docker-build
  script:
    - echo "$KUBECONFIG_DATA" > kubeconfig
    - export KUBECONFIG=$CI_PROJECT_DIR/kubeconfig
    - sed -i.bak "s~FRONTEND_IMAGE_PLACEHOLDER~$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard~g" ./kubernetes/deployment.yaml
    - sed -i.bak "s~ADMIN_IMAGE_PLACEHOLDER~$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin~g" ./kubernetes/deployment.yaml
    - sed -i.bak "s~BRANCH_ID_PLACEHOLDER~branch-$(echo $CI_COMMIT_BRANCH | sha1sum | tr -dc a-z0-9 | head -c 16)~g" ./kubernetes/deployment.yaml
    - sed -i.bak "s~DEF_BRANCH~branch-$(echo $CI_DEFAULT_BRANCH | sha1sum | tr -dc a-z0-9 | head -c 16)~g" ./kubernetes/deployment.yaml
    - kubectl apply -f ./kubernetes/deployment.yaml

notify:
  stage: notify
  image: alpine:latest
  dependencies:
    - deploy
  script:
    - apk add --no-cache git sed coreutils bash curl
    - export DEPLOYMENT_URL=$(bash ./scripts/get-deployment-url.sh)
    - echo "$DEPLOYMENT_URL" > deployment_url.txt
    - |
      export DEPLOYMENT_MESSAGE="Updated deployment of $CI_PROJECT_NAME (branch $CI_COMMIT_BRANCH) to $DEPLOYMENT_URL"
      echo "$DEPLOYMENT_MESSAGE"
      export SLACK_MESSAGE="{\"text\":\"$DEPLOYMENT_MESSAGE\"}"
      curl -X POST -H 'Content-type: application/json' --data "$SLACK_MESSAGE" "$SLACK_WEBHOOK_URL"
  artifacts:
    paths:
      - deployment_url.txt
