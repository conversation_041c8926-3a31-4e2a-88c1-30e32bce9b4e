{"project": "mass", "general": "General", "subscription": "Subscription", "subscriptions": "Subscriptions", "accept": "Accept", "ppdl": "PDPL", "ppdl-long": "Personal Data Protection Law", "user-agreement": "User Agreement", "notification": "Notification", "notifications": "Notifications", "complaints-requests": "Complaints & Requests", "settings-help": "Settings & Help", "settings": "Settings", "help-support": "Help & Support", "FAQ-long": "Frequently Asked Questions", "about-us": "About Us", "user-guide": "User Guide", "logout": "Logout", "production": "Production", "consumption": "Consumption", "filter": "Filter", "cancel": "Cancel", "add": "Add", "add-new": "Add New", "clear-filters": "Clear Filters", "filters-applied": "Filters applied!", "filters-cleared": "Filters cleared!", "something-went-wrong": "Something went wrong!", "subscription-type": "Subscription Type", "facility-type": "Facility Type", "distribution-company": "Distribution Company", "all": "All", "neutral-person": "Neutral Person", "artificial-person": "Artificial Person", "select-an-option": "Select an option...", "no-results": "No results found", "fill-all-required-fields": "Please fill all required fields.", "operation-canceled": "Operation canceled!", "operation-completed": "Operation completed!", "installation-id": "Installation ID", "person-identifier": "Taxpayer Identification Number (TIN)", "person-identifier-real": "TC Identity Number", "edit": "Edit", "delete": "Delete", "update": "Update", "confirm": "Confirm", "subscription-informations": "Subscription Informations", "subscription-name": "Subscription Name", "address": "Address", "subscription-date": "Subscription Start Date", "clear": "Clear", "apply": "Apply", "period-type": "Period Type", "daily": "Daily", "monthly": "Monthly", "yearly": "Yearly", "date": "Date", "start-end-date": "Start Date - End Date", "start-date": "Start Date", "end-date": "End Date", "date-range": "Date Range", "analyze-type": "Analyze Type", "select-range": "Please select a date range.", "select-range-x-to-y": "Please select a date range between {{x}} and {{y}} days.", "consumer-data": "Consumer Data", "file-type": "File Type", "year": "Year", "month": "Month", "day": "Day", "hour": "Hour", "export": "Export", "wait": "Please wait...", "exported": "Exported successfully!", "user-defined-limit": "User Defined Limit", "unexpected-overconsumption-threshold": "Unexpected Overconsumption Warning Threshold", "results": "Results", "table": "Table", "chart": "Chart", "hourly": "Hourly", "hourly-view": "Hourly View", "monochronic": "Monochronic", "polychronic": "Polychronic", "default": "<PERSON><PERSON><PERSON>", "z-a": "Most to Least", "a-z": "Least to Most", "total-consumption": "Total Consumption", "total-production": "Total Production", "t1": "Daytime", "t2": "Peak", "t3": "Night", "average": "Average", "city-average": "City Average", "district-average": "District Average", "total-x-entries": "Total {{count}} Entries", "page-x-of-y": "Page {{current}} of {{total}}", "total": "Total", "type-or-date": "Type / Date", "anon": "Anonymous"}