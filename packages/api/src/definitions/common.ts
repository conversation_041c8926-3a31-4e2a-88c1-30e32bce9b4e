import type { BlobType, EmptyType, Fn } from '@mass/utils'
import type { Type } from 'arktype'

import type { authApi } from '../services/auth'
import type { settingApi } from '../services/setting'
import type { subscriptionsApi } from '../services/subscriptions'

declare global {
  namespace Api {
    interface Definition {
      cache?: 'none' | 'until-reload' | 'validate'
      method?: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'window.open'
      url: string

      query?: Type
      params?: Type
      payload?: Type
      response?: Type<unknown>

      encodeParamsKeysUrl?: string[]

      cb?: Fn<BlobType[], BlobType>
    }

    type GetUrl<D extends Api.Definition> = D['url']
    type GetMethod<D extends Api.Definition> = D['method']
    type GetPayload<D extends Api.Definition> = D['payload'] extends Type<infer T> ? T : EmptyType
    type GetQuery<D extends Api.Definition> = D['query'] extends Type<infer T> ? T : EmptyType
    type GetParams<D extends Api.Definition> = D['params'] extends Type<infer T> ? T : EmptyType
    type GetResponse<D extends Api.Definition> = D['response'] extends Type<infer T> ? T : EmptyType

    type ExtractResponse<T> = T extends Fn<BlobType[], infer R> ? Awaited<R> : T
    type ExtractQuery<T> = T extends Fn<[options: infer O], BlobType>
      ? Exclude<O, undefined> extends { query?: infer Q }
        ? Q
        : never
      : never
    type ExtractParams<T> = T extends Fn<[options: infer O], BlobType>
      ? Exclude<O, undefined> extends { params?: infer P }
        ? P
        : never
      : never
    type ExtractPayload<T> = T extends Fn<[options: infer O], BlobType>
      ? Exclude<O, undefined> extends { payload?: infer P }
        ? P
        : never
      : never

    interface Services {
      auth: typeof authApi
      setting: typeof settingApi
      subscriptions: typeof subscriptionsApi
    }
  }
}
