import { type } from 'arktype'

import { apiBuilder } from '../utils/api-builder'

const subscriptionContent = type({
  id: 'string',
  createdAt: 'string',
  name: 'string',
  userId: 'string',
  type: "'electricity-production' | 'electricity-consumption'",
  individual: 'boolean',
  personIdentifier: type.string.atLeastLength(10).atMostLength(10),
  regionId: 'string',
  installationId: 'string',
  startDate: 'string',
  key: 'string',
  deletedAt: 'null',
  userDefinedLimit: 'null',
  unexpectedUsageThreshold: 'number',

  details: {
    address: 'string',
  },
})

export const newSubscriptionPayload = subscriptionContent
  .pick('name', 'individual', 'personIdentifier', 'regionId', 'installationId')
  .required()

export const updateSubscriptionPayload = subscriptionContent.pick('name').required()

export const subscriptionData = type({
  // biome-ignore lint/style/useNamingConvention: Redundant
  T0: 'number',
  // biome-ignore lint/style/useNamingConvention: Redundant
  T1: 'number',
  // biome-ignore lint/style/useNamingConvention: Redundant
  T2: 'number',
  // biome-ignore lint/style/useNamingConvention: Redundant
  T3: 'number',
})
export const subscriptionUsageData = type({
  timeframe: 'string',
  data: subscriptionData,
})
export const subscriptionUsageCompare = type({
  average: subscriptionData,
  cityEntry: subscriptionData,
  districtEntry: subscriptionData,
})

export const subscriptionsApi = {
  regions: apiBuilder({
    url: '/region',
    cache: 'until-reload',

    response: type({
      id: 'string',
      name: 'string',
    }).array(),
  }),

  list: apiBuilder({
    url: '/subscription',
    cache: 'validate',

    params: type({
      'filter:eq?': subscriptionContent.partial().array(),
    }),

    encodeParamsKeysUrl: ['filter:eq'],

    response: type({
      content: subscriptionContent.array(),
      pageable: {
        pageNumber: 'number',
        pageSize: 'number',
        sort: {
          sorted: 'boolean',
          empty: 'boolean',
          unsorted: 'boolean',
        },
        offset: 'number',
        paged: 'boolean',
        unpaged: 'boolean',
      },
      last: 'boolean',
      totalPages: 'number',
      totalElements: 'number',
      size: 'number',
      number: 'number',
      sort: {
        sorted: 'boolean',
        empty: 'boolean',
        unsorted: 'boolean',
      },
      first: 'boolean',
      numberOfElements: 'number',
      empty: 'boolean',
    }),
  }),

  create: apiBuilder({
    method: 'POST',
    url: '/subscription',

    payload: newSubscriptionPayload,

    response: type('unknown'),
  }),

  update: apiBuilder({
    method: 'PATCH',
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    payload: updateSubscriptionPayload,

    response: type('unknown'),
  }),

  delete: apiBuilder({
    method: 'DELETE',
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    response: type('unknown'),
  }),

  detail: apiBuilder({
    url: '/subscription/$id',

    query: type({
      id: 'string',
    }),

    response: subscriptionContent,
  }),

  usage: {
    getLimits: apiBuilder({
      cache: 'validate',
      url: '/setting/global/subscriptions.usage.date-limits',

      response: type({
        value: {
          yearMax: 'string.alphanumeric',
          monthPrev: 'string.alphanumeric',
          dayMax: 'string.alphanumeric',
          dayPrev: 'string.alphanumeric',
          monthMax: 'string.alphanumeric',
        },
      }),
    }),

    data: apiBuilder({
      url: '/subscription/$id/usage',

      query: type({
        id: 'string',
      }),

      params: type({
        startDate: 'string',
        endDate: 'string',
        granularity: "'hour' | 'day' | 'month'",
        compareTo: type("'average' | 'similar-consumer-city' | 'similar-consumer-district'").array().optional(),
      }),

      response: type({
        production: type({
          unit: 'string',
          usageData: subscriptionUsageData.array(),
          compare: subscriptionUsageCompare,
        }).optional(),

        consumption: {
          unit: 'string',
          usageData: subscriptionUsageData.array(),
          compare: subscriptionUsageCompare,
        },
      }),
    }),
  },

  outages: {
    export: apiBuilder({
      method: 'POST',
      url: '/subscription/$id/outages/export',

      query: type({
        id: 'string',
      }),

      params: type({
        startDate: 'string',
        endDate: 'string',
        fileType: "'pdf' | 'csv' | 'xlsx'",
        language: "'tr' | 'en'",
      }),

      response: type({
        files: type({
          mimetype: 'string',
          fileName: 'string',
          url: 'string',
          size: 'number',
        }).array(),
      }),
    }),
  },

  notifications: {
    update: apiBuilder({
      method: 'PATCH',
      url: '/subscription/$id/notification',

      query: type({
        id: 'string',
      }),

      payload: type({
        unexpectedUsageThreshold: 'number',
        userDefinedLimit: 'number',
      }),

      response: type('unknown'),
    }),
  },
} as const
