import { use$ } from '@legendapp/state/react'
import { WarningIcon } from '@mass/icons'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

import { Button } from '../common/button'
import { <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>alogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogRoot } from '../common/dialog'
import { Text } from '../common/text'
import { ui$ } from '../stores/ui'

export const ConfirmationModal: FC = memo(() => {
  const { t: common } = useTranslation('common')

  const confirmation = use$(() => ui$.confirmation.get())

  const handleCancel = async () => {
    ui$.confirmation.set(null)
    ui$.onChangeModal('confirmation', false)

    if (confirmation?.onCancel) {
      await (confirmation.onCancel as () => Promise<void>)()
    }

    toast.success(common('operation-canceled'))
  }

  const handleConfirm = async () => {
    try {
      if (confirmation?.onConfirm) {
        await (confirmation.onConfirm as () => Promise<void>)()
      }

      toast.success(common('operation-completed'))

      ui$.onChangeModal('confirmation', false)
      ui$.confirmation.set(null)
    } catch (err) {
      console.log(err)
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='confirmation'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[250px]! sm:max-w-[400px]'
          header={
            <DialogHeader
              icon={<WarningIcon strokeWidth={2} className='h-12 w-12 text-warning' />}
              title={confirmation?.title ?? ''}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleCancel}>
                {common('cancel')}
              </Button>
              <Button variant='error' onClick={handleConfirm}>
                {common('confirm')}
              </Button>
            </DialogFooter>
          }>
          <Text variant='dim-2' className='text-sm'>
            {confirmation?.description ?? ''}
          </Text>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
