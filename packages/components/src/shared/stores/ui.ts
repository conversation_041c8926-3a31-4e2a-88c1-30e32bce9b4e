import { ObservableHint, observable } from '@legendapp/state'

export const ui$ = observable<Shared.UIStore>({
  modal: [],

  isModalOpen: ObservableHint.function((modal): boolean => {
    const raw = ui$.modal.get()

    if (raw === false) {
      return false
    }

    return raw.includes(modal)
  }),

  onChangeModal: ObservableHint.function((modal, open) => {
    ui$.modal.set(prev => {
      if (prev === false) {
        return open ? [modal] : false
      }

      if (open) {
        if (prev.includes(modal)) {
          return prev
        }

        return [...prev, modal]
      }

      if (!prev.includes(modal)) {
        return prev
      }

      return prev.filter(m => m !== modal)
    })
  }),

  onCloseAllModals: ObservableHint.function(() => {
    ui$.modal.set(false)
  }),

  confirmation: null,
})
