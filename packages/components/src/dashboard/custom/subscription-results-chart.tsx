/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: <explanation> */
import { use$ } from '@legendapp/state/react'
import { type BlobType, useGranularityFormat } from '@mass/utils'
import { type FC, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Bar,
  BarChart,
  Brush,
  CartesianGrid,
  Legend,
  Rectangle,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

import { subscription$ } from '../stores/subscription'

interface DisplayData {
  name: string
  label: Date | string
  compare?: string[]

  t0: number
  t1: number
  t2: number
  t3: number

  p0?: number

  identifier: string
}

interface MergedDisplayData extends DisplayData {
  oppositeCompare?: string[]

  pt0?: number
  pt1?: number
  pt2?: number
  pt3?: number
}

const compareMapping = {
  average: ['average', 1],
  'city-average': ['similar-consumer-city', 2],
  'district-average': ['similar-consumer-district', 3],
} as const

const colorGroups = [
  ['#7CD4FD', '#36BFFA', '#0BA5EC'],
  ['#FDE272', '#FAC515', '#EAAA08'],
  ['#A6EF67', '#85E13A', '#66C61C'],
  ['#EEAAFD', '#E478FA', '#D444F1'],
  ['#FEA3B4', '#FD6F8E', '#F63D68'],
]

const oppositeColorGroups = [
  ['#EF6820', '#EF6820', '#EF6820'], // opposites: #7CD4FD, #36BFFA, #0BA5EC
  ['#B692F6', '#B692F6', '#B692F6'], // opposites: #FDE272, #FAC515, #EAAA08
  ['#591098', '#591098', '#591098'], // opposites: #A6EF67, #85E13A, #66C61C
  ['#115502', '#115502', '#115502'], // opposites: #EEAAFD, #E478FA, #D444F1
  ['#015C4B', '#015C4B', '#015C4B'], // opposites: #FEA3B4, #FD6F8E, #F63D68
]

const CustomBar = (type: 't0' | 't1' | 't2' | 't3' | 'pt0' | 'pt1' | 'pt2' | 'pt3') => {
  return (props: { compare?: string[]; oppositeCompare?: string[]; [key: string]: BlobType }) => {
    const { compare, oppositeCompare } = props

    if (type === 't0') {
      return <Rectangle {...props} fill={compare?.[2] || colorGroups[0]?.[2] || '#0BA5EC'} />
    }

    if (type === 'pt0') {
      return <Rectangle {...props} fill={oppositeCompare?.[2] || oppositeColorGroups[0]?.[2] || '#EF6820'} />
    }

    const i = type === 't1' || type === 'pt1' ? 0 : type === 't2' || type === 'pt2' ? 1 : 2

    if (type.startsWith('p')) {
      return <Rectangle {...props} fill={oppositeCompare?.[i] || oppositeColorGroups[0]?.[i] || '#EF6820'} />
    }

    return <Rectangle {...props} fill={compare?.[i] || colorGroups[0]?.[i] || '#0BA5EC'} />
  }
}

const NUMBER_FORMAT = new Intl.NumberFormat('es-ES', {
  useGrouping: true,
  maximumFractionDigits: 2,
})

export const SubscriptionResultsChart: FC = () => {
  const { t } = useTranslation('dashboard')

  const populated = use$(() => subscription$.usage.populated.get())
  const timeMode = use$(() => subscription$.usage.timeMode.get())
  const granularity = use$(() => subscription$.query.populated.granularity.get())
  const selectedSubscription = use$(() => subscription$.selectedSubscription.get())

  const granularityFormat = useGranularityFormat(granularity)

  const [mergedData, setMergedData] = useState<MergedDisplayData[]>([])

  const isProduction = selectedSubscription?.type === 'electricity-production'
  const isThreeTime = timeMode === 'polychronic'

  useEffect(() => {
    const newDisplayData: DisplayData[] = populated.map(d => {
      const isComparisonData = typeof d.name === 'string'

      const displayItem: DisplayData = {
        name: isComparisonData ? t(`usage.compare.${d.name}`) : granularityFormat(d.name),
        label: d.name,
        t0: d.t0,
        t1: d.t1,
        t2: d.t2,
        t3: d.t3,
        identifier: isComparisonData ? (d.name as string) : (d.name as Date).toISOString(),
      }

      if (d.p0 !== undefined) {
        displayItem.p0 = d.p0
      }

      if (isComparisonData) {
        const colorIndex = compareMapping[d.name as keyof typeof compareMapping]?.[1] || 0
        const colors = colorGroups[colorIndex]
        if (colors) {
          displayItem.compare = colors
        }
      }

      return displayItem
    })

    // Separate comparison data from usage data
    const comparisonData = newDisplayData.filter(d => typeof d.label === 'string')
    const usageData = newDisplayData.filter(d => typeof d.label !== 'string')

    // For production data, we need to merge production values with consumption data
    if (isProduction) {
      const finalData = usageData.map(usageItem => {
        const mergedItem: MergedDisplayData = {
          ...usageItem,
          pt0: usageItem.p0 || 0,
          pt1: usageItem.p0 || 0, // Production typically uses T1 for total
          pt2: 0,
          pt3: 0,
        }

        return mergedItem
      })

      // Add comparison data
      const finalMergedData = [...finalData, ...comparisonData]
      setMergedData(finalMergedData)
    } else {
      setMergedData(newDisplayData)
    }
  }, [populated, hook.js:608 Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
    at getRootForUpdatedFiber (react-dom_client.js?v=384ca778:3001:128)
    at enqueueConcurrentRenderForLane (react-dom_client.js?v=384ca778:2989:16)
    at forceStoreRerender (react-dom_client.js?v=384ca778:4653:21)
    at Object.callback (react-dom_client.js?v=384ca778:4639:43)
    at recharts.js?v=384ca778:29924:21
    at defaultNoopBatch (recharts.js?v=384ca778:29910:3)
    at Object.notify (recharts.js?v=384ca778:29921:7)
    at Object.notifyNestedSubs (recharts.js?v=384ca778:29990:15)
    at handleChangeWrapper (recharts.js?v=384ca778:29994:20)
    at wrappedListener (recharts.js?v=384ca778:6683:50)

The above error occurred in the <ChartDataContextProvider> component.

React will try to recreate this component tree from scratch using the error boundary you provided, CatchBoundaryImpl.
overrideMethod	@	hook.js:608, t, isProduction])

  const formatter = (value: string) => {
    try {
      const date = new Date(value)

      if (Number.isNaN(date.getDate())) {
        return value.toString()
      }

      if (granularity === 'month') {
        // mm-yy
        return `${(date.getMonth() + 1).toString()}-${date.getFullYear().toString().slice(-2)}`
      }

      if (granularity === 'day') {
        // dd
        return `${date.getDate()}`
      }

      if (granularity === 'hour') {
        // hh:mm
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
    } catch (error) {
      console.warn('Error formatting date:', error)
    }

    return value
  }

  const tooltipFormatter = (value: string) => {
    try {
      const date = new Date(value)

      if (Number.isNaN(date.getDate())) {
        return value.toString()
      }

      return granularityFormat(date)
    } catch (error) {
      console.warn('Error formatting tooltip date:', error)
    }

    return value
  }

  return (
    <ResponsiveContainer width='100%' height='100%'>
      <BarChart
        data={mergedData}
        margin={{
          top: 0,
          right: 0,
          left: 0,
          bottom: 0,
        }}>
        <CartesianGrid vertical={false} />
        <XAxis dataKey='label' fontSize={12} tickFormatter={formatter} />
        <YAxis unit=' kWh' fontSize={12} />
        <Legend verticalAlign='top' wrapperStyle={{ lineHeight: '40px' }} />
        <Tooltip
          cursor={false}
          content={({ payload, label: _label }) => {
            if (!payload || payload.length === 0) {
              return null
            }
            return (
              <div className='rounded-lg bg-white p-3 shadow-lg'>
                <div className='mb-1 font-xs'>
                  {compareMapping[_label as keyof typeof compareMapping]
                    ? t(`usage.compare.${compareMapping[_label as keyof typeof compareMapping][0]}`)
                    : tooltipFormatter(String(_label || ''))}
                </div>
                <div className='flex flex-col'>
                  {payload
                    .map((entry, index) => ({ entry, index }))
                    .map(({ entry, index: rawIndex }) => {
                      const _ = entry.payload
                      const index = rawIndex % 3

                      const color =
                        entry.dataKey?.toString()?.startsWith('p') && (_.oppositeCompare ?? _.compare)
                          ? (_.oppositeCompare ?? _.compare)[index]
                          : _.compare
                            ? _.compare[index]
                            : entry.dataKey?.toString()?.startsWith('p')
                              ? oppositeColorGroups[0]?.[index] || '#EF6820'
                              : colorGroups[0]?.[index] || '#0BA5EC'

                      return (
                        <div key={entry.dataKey} className='mb-1 flex items-center justify-center text-sm'>
                          <div className='mr-1 h-2 w-2 rounded-full' style={{ backgroundColor: color }} />
                          <span className='flex-1'>
                            {t(`usage.${entry.name?.toString() || 'unknown'}`)}:{' '}
                            {NUMBER_FORMAT.format(+(entry.value ?? 0))} kWh
                          </span>
                        </div>
                      )
                    })}
                </div>
              </div>
            )
          }}
        />

        {isThreeTime ? (
          [...new Array(3)]
            .map((_, i) => i)
            .reverse()
            .map(i => (
              <Bar
                key={`t${i + 1}`}
                dataKey={`t${i + 1}`}
                stackId='a'
                maxBarSize={80}
                shape={CustomBar(`t${i + 1}` as 't1' | 't2' | 't3')}
              />
            ))
        ) : (
          <Bar dataKey='t0' maxBarSize={80} shape={CustomBar('t0')} />
        )}

        {isProduction && <Bar dataKey='pt0' maxBarSize={80} shape={CustomBar('pt0')} />}

        <Brush dataKey='name' height={30} stroke='#0d9fe7' travellerWidth={10} />
      </BarChart>
    </ResponsiveContainer>
  )
}
