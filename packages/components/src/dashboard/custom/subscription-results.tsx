import { use$ } from '@legendapp/state/react'
import { type BlobType, useGranularityFormat, useNumberFormat } from '@mass/utils'
import type { Row } from '@tanstack/react-table'
import clsx from 'clsx'
import { type FC, memo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { Button, Select, Table, type TableColumnOptions, Text, Title } from '../../shared'
import { MAPPED_PERIODS, subscription$ } from '../stores/subscription'
import { SubscriptionResultsChart } from './subscription-results-chart'

const DATA = [] as BlobType

const useTableColumns = () => {
  const { t: common } = useTranslation('common')

  const isProduction = use$(() => subscription$.selectedSubscription.get()?.type === 'electricity-production')
  const timeMode = use$(() => subscription$.usage.timeMode.get())
  const mode = use$(() => subscription$.usage.mode.get())
  const populatedGranularity = use$(() => subscription$.query.populated.granularity.get())
  const period = use$(() => subscription$.query.values.period.get())

  const granularityFormat = useGranularityFormat(
    populatedGranularity === 'hour' && mode === 'table' ? MAPPED_PERIODS[period] : populatedGranularity,
  )
  const numberFormat = useNumberFormat()

  const numberSort = useCallback((field: keyof Dashboard.PopulatedUsages) => {
    return (rowA: Row<Dashboard.PopulatedUsages>, rowB: Row<Dashboard.PopulatedUsages>) => {
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const a = (rowA.original[field]! as number) || 0
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const b = (rowB.original[field]! as number) || 0

      return a - b
    }
  }, [])

  const numberRenderer = useCallback(
    (value: number) => {
      const formatted = numberFormat(value)

      return <Text variant='dim'> {formatted === 'NaN' ? '-' : formatted} </Text>
    },
    [numberFormat],
  )

  return [
    {
      key: 'name',
      label: mode === 'table' ? common('type-or-date') : common('date'),
      description: 'deneme 123',

      sortable: (rowA, rowB) => {
        const a = +rowA.original.name
        const b = +rowB.original.name

        if (Number.isNaN(a) || Number.isNaN(b)) {
          return 0
        }

        return a - b
      },

      render(value) {
        return <Text variant='dim'> {value instanceof Date ? granularityFormat(value) : common(value)} </Text>
      },
    },
    ...(isProduction
      ? [
          {
            key: 'p0',
            label: common('total-production'),
            additionalLabel: '(kWh)',

            sortable: numberSort('p0'),
            render: numberRenderer,
          } satisfies TableColumnOptions<Dashboard.PopulatedUsages>,
        ]
      : []),
    {
      key: 't0',
      label: common('total-consumption'),
      additionalLabel: '(kWh)',

      sortable: numberSort('t0'),
      render: numberRenderer,
    },

    ...(timeMode === 'polychronic' && mode !== 'hourly'
      ? [
          {
            key: 't1',
            label: common('t1'),
            additionalLabel: '(kWh)',

            sortable: numberSort('t1'),
            render: numberRenderer,
          } satisfies TableColumnOptions<Dashboard.PopulatedUsages>,
          {
            key: 't2',
            label: common('t2'),
            additionalLabel: '(kWh)',

            sortable: numberSort('t2'),
            render: numberRenderer,
          } satisfies TableColumnOptions<Dashboard.PopulatedUsages>,
          {
            key: 't3',
            label: common('t3'),
            additionalLabel: '(kWh)',

            sortable: numberSort('t3'),
            render: numberRenderer,
          } satisfies TableColumnOptions<Dashboard.PopulatedUsages>,
        ]
      : []),
  ] satisfies TableColumnOptions<Dashboard.PopulatedUsages>[]
}

export const SubscriptionResults: FC = memo(() => {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const columns = useTableColumns()
  const isLoading = use$(() => subscription$.usage.isLoading.get())
  const mode = use$(() => subscription$.usage.mode.get())
  const timeMode = use$(() => subscription$.usage.timeMode.get())
  const usageExists = use$(() => subscription$.usage.data.get() !== null)
  const populatedGranularity = use$(() => subscription$.query.populated.granularity.get())

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-6 md:gap-12',
        {
          'pointer-events-none opacity-30': isLoading || !usageExists,
        },
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between gap-8 lg:flex-row', // flex
          'relative w-full overflow-hidden',
          'transition-opacity duration-300',
        )}>
        <div className='flex flex-col gap-2'>
          <Title el='h1' variant='h4'>
            {common('results')}
          </Title>

          <Text variant='dim-2'> {dashboard('subscriptions.query-results')} </Text>
        </div>

        <div className='flex flex-col items-center gap-4 md:flex-row'>
          <div className='flex w-full flex-row items-center gap-4'>
            <Select
              value={timeMode}
              options={[
                { value: 'monochronic', label: common('monochronic') },
                ...(mode === 'hourly' ? [] : [{ value: 'polychronic', label: common('polychronic') }]),
              ]}
              onValueChange={value => subscription$.usage.timeMode.set(value)}
              className='max-w-[350px] rounded-c1'
              disabled={isLoading}
            />

            <Select
              value={mode}
              options={[
                { value: 'table', label: common('table') },
                { value: 'chart', label: common('chart') },
                ...(populatedGranularity === 'hour' ? [{ value: 'hourly', label: common('hourly-view') }] : []),
              ]}
              onValueChange={value => {
                if (value === 'hourly') {
                  subscription$.usage.timeMode.set('monochronic')
                }
                subscription$.usage.mode.set(value)
              }}
              className='max-w-[350px] rounded-c1'
              disabled={isLoading}
            />
          </div>

          <Button variant='primary' className='text-nowrap rounded-c1' disabled={isLoading}>
            {common('export')}
          </Button>
        </div>
      </div>

      {mode !== 'chart' && <Table data={subscription$.usage.populated} columns={columns} />}
      {mode === 'chart' && <SubscriptionResultsChart />}
    </div>
  )
})
