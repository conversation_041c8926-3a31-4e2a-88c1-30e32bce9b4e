import { use$ } from '@legendapp/state/react'
import { ChevronDownIcon, HomeIcon } from '@mass/icons'
import { useLocation } from '@tanstack/react-router'
import clsx from 'clsx'
import { type FC, Fragment, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Link, Text } from '../../../shared'
import { SUBSCRIPTION_REGEX } from '../../hooks/use-meta'
import { subscription$ } from '../../stores/subscription'

export const Breadcrumbs: FC = () => {
  const { t: dashboard } = useTranslation('dashboard')
  const { pathname } = useLocation()
  const subscription = use$(() => subscription$.selectedSubscription.get())

  const el = useMemo(() => {
    const list: React.ReactNode[] = []

    let isHome = false

    if (pathname === '/' || SUBSCRIPTION_REGEX.test(pathname)) {
      isHome = true
    }

    if (isHome) {
      list.unshift(
        <Link to='/'>
          <Text variant='dim-2-semibold'> {dashboard('subscriptions.title')} </Text>
        </Link>,
      )
      list.unshift(<HomeIcon className='text-dim-2' strokeWidth={1.5} />)
    }

    if (SUBSCRIPTION_REGEX.test(pathname)) {
      list.push(
        <Link to='/$subscriptionId' params={{ subscriptionId: subscription?.id }}>
          <Text variant='dim-2-semibold'> {subscription?.name} </Text>
        </Link>,
      )
    }

    return list.map((item, index) => (
      <Fragment
        // biome-ignore lint/suspicious/noArrayIndexKey: Redundant
        key={`subscription-Breadcrumbs-${index}`}>
        {item}
        {index !== list.length - 1 && <ChevronDownIcon className='-rotate-90 text-dim-3' width={16} height={16} />}
      </Fragment>
    ))
  }, [pathname, dashboard, subscription])

  return (
    <section
      className={clsx(
        'flex items-center gap-4', // flex
      )}>
      {el}
    </section>
  )
}
